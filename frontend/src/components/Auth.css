/* Authentication Components Styles */

.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-in-out;
}

.auth-modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: slideIn 0.3s ease-out;
}

.auth-form {
    padding: 2rem;
}

.auth-form h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
    font-size: 1.8rem;
    font-weight: 700;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: white;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group input:invalid {
    border-color: #f44336;
}

.auth-button {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.auth-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.auth-button:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    border-left: 4px solid #f44336;
    font-size: 0.9rem;
    font-weight: 500;
}

.success-message {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    border-left: 4px solid #4CAF50;
    font-size: 0.9rem;
    font-weight: 500;
}

.auth-switch {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e1e5e9;
}

.auth-switch p {
    margin-bottom: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.switch-button {
    background: none;
    border: none;
    color: #2196F3;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.switch-button:hover {
    color: #1976D2;
}

.auth-close-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.auth-close-button:hover {
    background: #f5f5f5;
    color: #333;
}

/* User Profile Dropdown */
.user-profile {
    position: relative;
    display: inline-block;
}

.user-profile-button {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-profile-button:hover {
    background: linear-gradient(135deg, #1976D2, #1565C0);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    margin-top: 0.5rem;
    border: 1px solid #e1e5e9;
}

.user-dropdown-item {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-dropdown-item:hover {
    background: #f8f9fa;
}

.user-dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.user-dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
    color: #f44336;
}

.user-dropdown-item:last-child:hover {
    background: #ffebee;
}

/* Connection Status */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    font-size: 0.8rem;
    color: white;
}

.connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.connection-indicator.connected {
    background: #4CAF50;
}

.connection-indicator.disconnected {
    background: #f44336;
    animation: none;
}

.connection-indicator.connecting {
    background: #ff9800;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .auth-form {
        padding: 1.5rem;
    }
    
    .auth-form h2 {
        font-size: 1.5rem;
    }
    
    .user-dropdown {
        right: -1rem;
        min-width: 180px;
    }
}
