/* Prediction Modal Styles */

.prediction-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-in-out;
}

.prediction-modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
}

.prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 2px solid #f0f0f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.prediction-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.match-info {
    padding: 1.5rem 2rem;
    text-align: center;
    background: #f8f9fa;
}

.match-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #333;
    font-weight: 700;
}

.bookmaker {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Prediction Form */
.prediction-form {
    padding: 2rem;
}

.prediction-form h5 {
    margin: 0 0 1.5rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.factor-group {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.factor-group label {
    flex: 1;
    font-weight: 600;
    color: #555;
    font-size: 0.9rem;
}

.factor-group input[type="range"] {
    flex: 2;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.factor-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.factor-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.factor-value {
    min-width: 30px;
    text-align: center;
    font-weight: 700;
    color: #4CAF50;
    background: #e8f5e8;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.predict-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.predict-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.predict-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Prediction Result */
.prediction-result {
    padding: 2rem;
}

.prediction-outcome {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.outcome-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.outcome-icon {
    font-size: 2rem;
}

.outcome-header h4 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
}

.confidence-meter {
    margin-top: 1rem;
}

.confidence-label {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.confidence-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    border-radius: 6px;
    transition: width 0.5s ease;
}

/* Probabilities */
.probabilities {
    margin-bottom: 2rem;
}

.probabilities h5 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.prob-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.prob-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.prob-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.prob-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
}

/* Prediction Details */
.prediction-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.risk-level, .expected-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

.risk-badge {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 700;
}

.positive {
    color: #4CAF50;
    font-weight: 700;
}

.negative {
    color: #f44336;
    font-weight: 700;
}

/* Reasoning */
.reasoning {
    margin-bottom: 2rem;
}

.reasoning h5 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.reasoning p {
    margin: 0;
    color: #555;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Prediction Actions */
.prediction-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.new-prediction-btn, .close-prediction-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.new-prediction-btn {
    background: #2196F3;
    color: white;
}

.new-prediction-btn:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.close-prediction-btn {
    background: #4CAF50;
    color: white;
}

.close-prediction-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to { 
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .prediction-modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .prediction-header {
        padding: 1rem 1.5rem;
    }
    
    .prediction-form, .prediction-result {
        padding: 1.5rem;
    }
    
    .prob-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .prediction-details {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .prediction-actions {
        flex-direction: column;
    }
    
    .factor-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .factor-group input[type="range"] {
        width: 100%;
    }
}
