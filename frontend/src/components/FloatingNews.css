/* Floating News Styles */

.floating-news {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 1500;
    max-width: 400px;
    min-width: 320px;
    border: 1px solid #e1e5e9;
    transition: all 0.3s ease;
    animation: slideInUp 0.5s ease-out;
}

.floating-news.expanded {
    max-width: 500px;
    max-height: 600px;
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.news-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.news-icon {
    font-size: 1.2rem;
}

.news-title {
    font-weight: 700;
    font-size: 1rem;
}

.news-controls {
    display: flex;
    gap: 0.5rem;
}

.expand-btn, .close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.expand-btn:hover, .close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Compact Ticker Mode */
.news-ticker {
    padding: 1rem 1.5rem;
    position: relative;
}

.news-item-compact {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.news-category-badge {
    flex-shrink: 0;
}

.category-icon {
    display: inline-block;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    font-weight: 700;
}

.news-content-compact {
    flex: 1;
    min-width: 0;
}

.news-title-compact {
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.news-meta-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
}

.news-source {
    font-weight: 600;
}

.news-time {
    font-style: italic;
}

.news-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #f0f0f0;
    border-radius: 0 0 16px 16px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    width: 0;
    border-radius: 0 0 16px 16px;
}

@keyframes progress {
    from { width: 0; }
    to { width: 100%; }
}

/* Expanded Mode */
.news-expanded {
    padding: 1.5rem;
    max-height: 500px;
    overflow-y: auto;
}

.current-news {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.news-category-full {
    margin-bottom: 1rem;
}

.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
}

.news-title-full {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.4;
}

.news-summary {
    margin: 0 0 1rem 0;
    color: #555;
    font-size: 0.9rem;
    line-height: 1.5;
}

.news-meta-full {
    margin-bottom: 1rem;
}

.news-source-full {
    font-weight: 600;
    color: #333;
    margin-right: 1rem;
}

.news-time-full {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

.teams-mentioned {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.teams-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 600;
}

.team-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.news-actions {
    text-align: center;
}

.read-more-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* News List */
.news-list {
    margin-top: 1.5rem;
}

.news-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.news-list-header h5 {
    margin: 0;
    color: #333;
    font-size: 1rem;
    font-weight: 700;
}

.refresh-news-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.refresh-news-btn:hover {
    background: #f0f0f0;
    transform: rotate(180deg);
}

.news-items-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.news-list-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.news-list-item:hover {
    background: #f8f9fa;
    border-color: #e1e5e9;
}

.news-list-item.active {
    background: #e3f2fd;
    border-color: #2196F3;
}

.list-item-category {
    flex-shrink: 0;
}

.list-category-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: white;
    font-weight: 700;
}

.list-item-content {
    flex: 1;
    min-width: 0;
}

.list-item-title {
    font-weight: 600;
    color: #333;
    font-size: 0.85rem;
    line-height: 1.3;
    margin-bottom: 0.25rem;
}

.list-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #666;
}

.list-item-source {
    font-weight: 600;
}

.list-item-time {
    font-style: italic;
}

/* News Indicators */
.news-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid #f0f0f0;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ddd;
}

.indicator-dot:hover {
    transform: scale(1.2);
}

.indicator-dot.active {
    transform: scale(1.3);
}

/* Animations */
@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-news {
        bottom: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
    }
    
    .floating-news.expanded {
        max-width: none;
    }
    
    .news-header {
        padding: 0.75rem 1rem;
    }
    
    .news-ticker {
        padding: 0.75rem 1rem;
    }
    
    .news-expanded {
        padding: 1rem;
    }
    
    .news-title-compact {
        font-size: 0.9rem;
    }
    
    .teams-mentioned {
        flex-direction: column;
        align-items: flex-start;
    }
}
