/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
        'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
        'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.App {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    text-align: center;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.app-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Controls */
.controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.refresh-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.refresh-btn:hover:not(:disabled) {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.refresh-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Loading and Error States */
.loading {
    text-align: center;
    padding: 3rem;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.error-message {
    background: #f44336;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
    font-weight: 600;
}

.no-matches {
    text-align: center;
    padding: 3rem;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.no-matches h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

/* Matches Grid */
.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.match-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.match-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.match-header h3 {
    font-size: 1.2rem;
    color: #333;
    font-weight: 700;
}

.bookmaker {
    background: #2196f3;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.match-time {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.best-value {
    background: linear-gradient(135deg, #4caf50, #45a049);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.best-bet {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.bet-type {
    font-weight: 600;
}

.value-percentage {
    font-size: 1.2rem;
    font-weight: 700;
}

.odds {
    font-size: 0.9rem;
    opacity: 0.9;
}

.all-values {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.value-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.value-row span:first-child {
    font-weight: 600;
    color: #333;
}

.positive {
    color: #4caf50;
    font-weight: 700;
}

.neutral {
    color: #999;
}

.odds-small {
    font-size: 0.8rem;
    color: #666;
}

/* Footer */
.app-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    text-align: center;
    color: white;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header h1 {
        font-size: 2rem;
    }

    .main-content {
        padding: 1rem;
    }

    .matches-grid {
        grid-template-columns: 1fr;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .match-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
