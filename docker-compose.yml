version: '3.8'

services:
    backend:
        build: ./backend
        container_name: betting-backend
        ports:
            - '8000:8000'
        volumes:
            - ./backend:/app
        working_dir: /app
        command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

    frontend:
        build: ./frontend
        container_name: betting-frontend
        ports:
            - '3000:3000'
        volumes:
            - ./frontend:/app
        working_dir: /app
        command: ['npm', 'start']
